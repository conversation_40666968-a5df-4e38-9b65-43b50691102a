<template>
    <YSXK-ScaleDiv
        :reduceW="scaleDivProps.reduceW"
        :reduceH="scaleDivProps.reduceH"
        :w="scaleDivProps.w"
        :h="scaleDivProps.h"
        ref="scaleDivRef"
    >
        <div class="largeScreen" ref="largeScreen">
            <img class="title"      src="../../../assets/images/largeScreen/title.png" />
            <img class="leftJump"   :src="LCR === 'left'   ? leftJump_c   : leftJump"   @click="jumpTemplate('left')" />
            <img class="centerJump" :src="LCR === 'center' ? centerJump_c : centerJump" @click="jumpTemplate('center')" />
            <img class="rightJump"  :src="LCR === 'right'  ? rightJump_c  : rightJump"  @click="jumpTemplate('right')" />

            <!-- 数据可视化 -->
            <left   v-if     ="LCR === 'left'" />
            <!-- 数字孪生 -->
            <center v-if     ="LCR === 'center'" />
            <!-- 调度中心 -->
            <right  v-else-if="LCR === 'right'" />

            <img class="bottom" src="../../../assets/images/largeScreen/bottom.png" />
            <img class="exit"   src="../../../assets/images/largeScreen/exit.png" @click="exitScreen" v-if="isFullScreen" />
            <img class="open"   src="../../../assets/images/largeScreen/open.png" @click="fullScreen" v-else />
            <a class="technicalSupport" href="http://yunsxk.com/" target="_blank">技术支持 - 重庆云昇新控智能科技有限公司</a>
        </div>
    </YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDynamicScale } from '/@/hooks/web/useDynamicScale'

import leftJump     from '../../../assets/images/largeScreen/leftJump.png'
import leftJump_c   from '../../../assets/images/largeScreen/leftJump_c.png'
import rightJump    from '../../../assets/images/largeScreen/rightJump.png'
import rightJump_c  from '../../../assets/images/largeScreen/rightJump_c.png'
import centerJump   from '../../../assets/images/largeScreen/centerJump.png'
import centerJump_c from '../../../assets/images/largeScreen/centerJump_c.png'

import left from './components/left.vue'
import center from './components/center.vue'
import right from './components/right.vue'
import YSXKScaleDiv from '@/views/dashboard/Analysis/components/YSXK-ScaleDiv.vue';

// 获取动态缩放值
const { dynamicReduceW, dynamicW } = useDynamicScale()

const LCR = ref('left')

const jumpTemplate = (lr: string) => {
    LCR.value = lr
    if (!isFullScreen.value) {
        fullScreen()
    }
}

const largeScreen = ref()
const scaleDivRef = ref(null)

const isFullScreen = ref(false)

// 根据全屏状态动态计算 YSXK-ScaleDiv 的 props
const scaleDivProps = computed(() => {
    if (isFullScreen.value) {
        // 全屏状态：使用固定的 1920x1080 尺寸
        return {
            reduceW: 0,
            reduceH: 0,
            w: 1920,
            h: 1080
        }
    } else {
        // 非全屏状态：使用动态缩放值
        return {
            reduceW: dynamicReduceW.value,
            reduceH: 60, // 保持默认的 reduceH 值
            w: dynamicW.value,
            h: 854 // 保持默认的 h 值
        }
    }
})

const fullScreen = () => {
  console.log("🚀 ~ scaleDivRef.value:",scaleDivRef.value)
  // 通过组件实例的 $el 属性访问根 DOM 元素
  if (scaleDivRef.value && scaleDivRef.value.$el) {
    scaleDivRef.value.$el.requestFullscreen()
      .then(() => {
        console.log("🚀 ~ 进入全屏成功")
      })
      .catch((err) => {
        console.error("🚀 ~ 进入全屏失败:", err)
      })
  } else {
    console.warn("🚀 ~ scaleDivRef 未找到")
  }
}

const exitScreen = () => {
    document.exitFullscreen()
}

// ESC 键监听函数
const handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isFullScreen.value) {
      event.preventDefault()
      exitScreen()
    }
}

// 全屏状态变化监听
const handleFullscreenChange = () => {
  console.log("🚀 ~ 111111111111:",111111111111)
    if (document.fullscreenElement) {
        isFullScreen.value = true
    } else {
        isFullScreen.value = false
    }
}

onMounted(() => {
    // 添加全屏状态变化监听
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    // 添加 ESC 键监听
    document.addEventListener('keydown', handleEscKey)
})

onUnmounted(() => {
    // 清理事件监听器
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
    document.removeEventListener('keydown', handleEscKey)
})
</script>

<style lang="less" scoped>
.largeScreen {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #ccc;
    // overflow: hidden;
    img {
        position: absolute;
        user-select: none;
    }
    .title {
        width: 1920px;
        height: 105px;
        top: 0;
        left: 0;
        z-index: 10;
    }
    .centerJump, .leftJump, .rightJump {
        width: calc(320 / 1920 * 100%);
        height: 56px;
        top: 22px;
        cursor: pointer;
        z-index: 10;
    }
    .leftJump {
        left: calc(320 / 1920 * 100%);
    }
    .centerJump {
        width: calc(400 / 1920 * 100%);
        height: 46px;
        left: 50%;
        top: 74px;
        transform: translateX(-50%);
    }
    .rightJump {
        right: calc(320 / 1920 * 100%);
    }
    .bottom {
        width: 100%;
        height: 46px;
        left: 0;
        bottom: 0;
        // position: fixed;
    }
    .open, .exit {
        width: 97px;
        height: 40px;
        cursor: pointer;
        right: 24%;
        bottom: 60px;
        position: fixed;
    }
    .technicalSupport {
        position: absolute;
        left: 50%;
        bottom: 9px;
        transform: translateX(-50%);
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0px -1px 10px #0143FF;
    }
}
</style>
