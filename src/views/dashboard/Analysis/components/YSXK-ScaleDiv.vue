// YSXK-ScaleDiv 缩放内容

<template>
  <div class="YSXK-ScaleDiv" :style="containerStyle">
    <div class="content-wrap" :style="scaleStyle">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface ScaleDivProps {
  w?: number
  h?: number
  reduceW?: number
  reduceH?: number
}

const props = defineProps<ScaleDivProps>()

const w = computed(() => {
  return props.w !== undefined ? props.w : 1688
})
const h = computed(() => {
  return props.h !== undefined ? props.h : 854
})
const reduceW = computed(() => {
  return props.reduceW !== undefined ? props.reduceW : 210
})
const reduceH = computed(() => {
  return props.reduceH !== undefined ? props.reduceH : 60
})

const scaleStyle = ref({
  width: `${w.value}px`,
  height: `${h.value}px`,
  transform: "scale(1) translate(-50%, -50%)" // 默认不缩放，垂直水平居中
})

// 将 containerStyle 改为 computed，使其能响应 props 变化
const containerStyle = computed(() => ({
  width: `calc(100vw - ${reduceW.value}px)`,
  height: `calc(100vh - ${reduceH.value}px)`
}))

// 设置缩放比例
const setScale = () => {
  // 更新 scaleStyle 的宽高
  scaleStyle.value.width = `${w.value}px`
  scaleStyle.value.height = `${h.value}px`

  const _w = (window.innerWidth - reduceW.value) / w.value
  const _h = (window.innerHeight - reduceH.value) / h.value
  // 注意 transform 属性会更改子元素 position: fixed; 定位
  scaleStyle.value.transform = `scale(${_w},${_h}) translate(-50%, -50%)`
  console.log("🚀 ~ setScale called, reduceW:", reduceW.value, "w:", w.value, "h:", h.value, "reduceH:", reduceH.value, "scale:", _w, _h)
  console.log("🚀 ~ window size:", window.innerWidth, window.innerHeight)
  console.log("🚀 ~ container size:", (window.innerWidth - reduceW.value), (window.innerHeight - reduceH.value))
}

// 监听 props 变化，重新计算缩放
watch([w, h, reduceW, reduceH], () => {
  console.log("🚀 ~ props changed, new values:", { w: w.value, h: h.value, reduceW: reduceW.value, reduceH: reduceH.value })
  setScale()
}, { immediate: true })

onMounted(() => {
  setScale()
  window.addEventListener("resize", setScale)
})

onUnmounted(() => {
  window.removeEventListener("resize", setScale)
})

export type {
  ScaleDivProps
}
</script>

<style lang="less" scoped>
.YSXK-ScaleDiv {
  position: relative;
  overflow: hidden;
  .content-wrap {
    // 注意 transform 属性会更改子元素 position: fixed; 定位
    transform-origin: 0 0;
    position: absolute;
    top: 50%;
    left: 50%;
  }
}
</style>
